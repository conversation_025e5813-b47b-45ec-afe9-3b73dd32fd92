<template>
  <BaseDrawer
    v-model="visible"
    title="规则验证测试"
    width="600px"
    :footer-visible="true"
    @cancel="handleCancel"
  >
    <div class="rule-test-dialog">
      <!-- 当前时间 -->
      <div class="test-info">
        <div class="info-item">
          <span class="info-label">当前时间：</span>
          <span class="info-value">{{ currentTime }}</span>
        </div>
      </div>

      <!-- 规则选择 -->
      <div class="form-section">
        <div class="section-title">规则选择</div>
        <a-select
          v-model:value="selectedRuleId"
          placeholder="选择规则或开启检测"
          style="width: 100%"
          allow-clear
        >
          <a-select-option v-for="rule in availableRules" :key="rule.id" :value="rule.id">
            {{ rule.ruleName }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 文本输入 -->
      <div class="form-section">
        <div class="section-title">文本输入</div>
        <a-textarea
          v-model:value="testText"
          placeholder="输入需要检测的内容..."
          :rows="6"
          :maxlength="1000"
          show-count
        />
      </div> 
      <!-- 测试结果 -->
      <div v-if="testResult" class="result-section">
        <div class="section-title">测试结果</div>
        <div class="result-content">
          <div class="result-item">
            <span class="result-label">命中词：</span>
            <span class="result-value">{{ testResult.matchedWords.join('、') || '无' }}</span>
          </div>
          <div class="result-item">
            <span class="result-label">组合：</span>
            <span class="result-value">{{ testResult.combination || '无' }}</span>
          </div>
          <div class="result-item">
            <span class="result-label">风险等级：</span>
            <a-tag :color="getRiskLevelColor(testResult.riskLevel)">
              {{ getRiskLevelText(testResult.riskLevel) }}
            </a-tag>
          </div>
          <div class="result-item">
            <span class="result-label">是否触发规则：</span>
            <a-tag :color="testResult.triggered ? 'red' : 'green'">
              {{ testResult.triggered ? '是' : '否' }}
            </a-tag>
          </div>
          <div class="result-item">
            <span class="result-label">置信度阀值：</span>
            <a-tag :color="testResult.triggered ? 'red' : 'green'">
              {{ testResult.triggered ? '高' : '中' }}
            </a-tag>
          </div>
          <!-- 高亮显示匹配文本 -->
          <div v-if="testResult.highlightedText" class="highlighted-text">
            <div class="result-label">匹配内容：</div>
            <div class="highlighted-content" v-html="testResult.highlightedText"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer> 
      <a-button @click="handleCancel">取消</a-button> 
      <a-button
        type="primary"
        :loading="testing"
        @click="handleTest"
      >
        <template #icon>
          <BugPlay :size="16" />
        </template>
        运行测试
      </a-button> 
    </template>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { BugPlay } from 'lucide-vue-next'
import { message } from 'ant-design-vue'
import { BaseDrawer } from '@/components/BaseDrawer'

// ==================== 类型定义 ====================
interface TestResult {
  matchedWords: string[]
  combination: string
  riskLevel: string
  triggered: boolean
  highlightedText?: string
}

interface Rule {
  id: string
  ruleName: string
  riskLevel: string
  enabled: boolean
}

// ==================== Props 和 Emits ====================
interface Props {
  modelValue: boolean
  rules: Rule[]
  selectedRule?: Rule
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  rules: () => [],
  selectedRule: undefined
})

const emit = defineEmits(['update:modelValue'])

// ==================== 响应式数据 ====================
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const selectedRuleId = ref<string>('')
const testText = ref('')
const testing = ref(false)
const testResult = ref<TestResult | null>(null)

// 当前时间
const currentTime = ref('')

// 可用规则（只显示已启用的规则）
const availableRules = computed(() => {
  return props.rules.filter(rule => rule.enabled)
})

// ==================== 方法 ====================
// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取风险等级颜色
const getRiskLevelColor = (riskLevel: string) => {
  const colorMap: Record<string, string> = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[riskLevel] || 'default'
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel: string) => {
  const textMap: Record<string, string> = {
    high: '高风险',
    medium: '中等风险',
    low: '低风险'
  }
  return textMap[riskLevel] || '未知'
}

// 处理测试
const handleTest = async () => {
  if (!selectedRuleId.value) {
    message.warning('请选择要测试的规则')
    return
  }
  
  if (!testText.value.trim()) {
    message.warning('请输入测试文本')
    return
  }

  testing.value = true
  
  try {
    // 模拟测试逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const selectedRule = availableRules.value.find(rule => rule.id === selectedRuleId.value)
    if (!selectedRule) return

    // 模拟测试结果
    const mockResult = simulateTest(testText.value, selectedRule)
    testResult.value = mockResult
    
    message.success('测试完成')
  } catch (error) {
    console.error('测试失败:', error)
    message.error('测试失败，请重试')
  } finally {
    testing.value = false
  }
}

// 模拟测试逻辑
const simulateTest = (text: string, rule: Rule): TestResult => {
  const testTextLower = text.toLowerCase()
  
  // 模拟关键词匹配
  const mockKeywords = ['限定', '仅限', '必须', '不得', '禁止', '排除', '指定']
  const matchedWords = mockKeywords.filter(keyword => 
    testTextLower.includes(keyword)
  )
  
  // 生成高亮文本
  let highlightedText = text
  matchedWords.forEach(word => {
    const regex = new RegExp(`(${word})`, 'gi')
    highlightedText = highlightedText.replace(
      regex,
      '<span class="highlight-match">$1</span>'
    )
  })
  
  // 模拟组合和触发逻辑
  const combination = matchedWords.length > 0 ? matchedWords.join(' + ') : ''
  const triggered = matchedWords.length > 0 && Math.random() > 0.3
  
  return {
    matchedWords,
    combination,
    riskLevel: rule.riskLevel,
    triggered,
    highlightedText: matchedWords.length > 0 ? highlightedText : undefined
  }
} 

// 处理取消
const handleCancel = () => {
  visible.value = false
  testResult.value = null
}

// ==================== 监听器 ====================
// 监听弹框打开，初始化数据
watch(visible, (newVal) => {
  if (newVal) {
    updateCurrentTime()
    // 如果有预选规则，自动选中
    if (props.selectedRule) {
      selectedRuleId.value = props.selectedRule.id
    }
  } else {
    // 关闭时重置数据
    selectedRuleId.value = ''
    testText.value = ''
    testResult.value = null
  }
})

// 定时更新时间
setInterval(updateCurrentTime, 1000)
</script>

<style lang="scss" scoped>
.rule-test-dialog {
  padding:24px;
  .test-info {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        color: #666;
        margin-right: 8px;
        font-weight: 500;
      }

      .info-value {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600; 
    margin: 0 0 16px 0;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: var(--main-6);
      border-radius: 2px;
    }
  }
 

  .result-section {
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;

    .result-content {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 6px;

      .result-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .result-label {
          min-width: 80px;
          color: #666;
          font-size: 14px;
        }

        .result-value {
          color: #333;
          font-weight: 500;
        }
      }
      
      .highlighted-text {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e8e8e8;

        .result-label {
          margin-bottom: 8px;
          display: block;
        }

        .highlighted-content {
          background: white;
          padding: 12px;
          border-radius: 4px;
          line-height: 1.6;

          :deep(.highlight-match) {
            background: #ff4d4f20;
            color: #ff4d4f;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
