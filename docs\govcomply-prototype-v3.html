<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GovComply Web - 政府采购合规审查系统 v3.0</title>
    <style>
        /* CSS变量系统 */
        :root {
            /* 主色调 */
            --primary-color: #1890ff;
            --primary-light: #40a9ff;
            --primary-dark: #096dd9;
            --success-color: #52c41a;
            --warning-color: #fa8c16;
            --error-color: #ff4d4f;
            
            /* 违规类型专用色 */
            --violation-geo: #ff4d4f;
            --violation-ownership: #fa541c;
            --violation-brand: #fa8c16;
            --violation-qualification: #faad14;
            --violation-performance: #1890ff;
            --violation-other: #722ed1;
            
            /* 置信度色彩 */
            --confidence-high: #ff4d4f;
            --confidence-medium: #fa8c16;
            --confidence-low: #faad14;
            
            /* 间距系统 */
            --space-xs: 4px;
            --space-sm: 8px;
            --space-md: 16px;
            --space-lg: 24px;
            --space-xl: 32px;
            --space-xxl: 48px;
            
            /* 字体规范 */
            --font-size-sm: 12px;
            --font-size-base: 14px;
            --font-size-lg: 16px;
            --font-size-xl: 20px;
            --font-size-xxl: 24px;
            
            /* 圆角和阴影 */
            --border-radius-base: 6px;
            --border-radius-lg: 8px;
            --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
            --box-shadow-card: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* 全局重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            font-size: var(--font-size-base);
            line-height: 1.5;
            color: rgba(0, 0, 0, 0.85);
            background-color: #f5f5f5;
        }
        
        /* 主导航栏 */
        .main-nav {
            background: white;
            border-bottom: 1px solid #f0f0f0;
            box-shadow: var(--box-shadow-card);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 64px;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--space-lg);
            height: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .nav-brand {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .nav-menu {
            display: flex;
            gap: var(--space-xl);
        }
        
        .nav-item {
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--border-radius-base);
            cursor: pointer;
            transition: all 0.2s ease;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 500;
        }
        
        .nav-item:hover {
            background: #f0f8ff;
            color: var(--primary-color);
        }
        
        .nav-item.active {
            background: var(--primary-color);
            color: white;
        }
        
        .nav-user {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            color: rgba(0, 0, 0, 0.65);
        }
        
        /* 主容器 */
        .main-container {
            margin-top: 64px;
            min-height: calc(100vh - 64px);
        }
        
        /* 页面容器 */
        .page {
            display: none;
            padding: var(--space-lg);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page.active {
            display: block;
        }
        
        /* 页面标题 */
        .page-header {
            margin-bottom: var(--space-lg);
        }
        
        .page-title {
            font-size: var(--font-size-xxl);
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: var(--space-sm);
        }
        
        .page-description {
            color: rgba(0, 0, 0, 0.65);
            font-size: var(--font-size-base);
        }
        
        /* 卡片组件 */
        .card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-card);
            padding: var(--space-lg);
            margin-bottom: var(--space-lg);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid #f0f0f0;
        }
        
        .card-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }
        
        /* 按钮组件 */
        .btn {
            padding: var(--space-sm) var(--space-md);
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            background: white;
            color: rgba(0, 0, 0, 0.85);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            text-decoration: none;
        }
        
        .btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
        }
        
        .btn-danger {
            background: var(--error-color);
            border-color: var(--error-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff7875;
            border-color: #ff7875;
            color: white;
        }
        
        .btn-small {
            padding: var(--space-xs) var(--space-sm);
            font-size: var(--font-size-sm);
        }
        
        /* 表格组件 */
        .table-container {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .table th,
        .table td {
            padding: var(--space-md);
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .table th {
            background: #fafafa;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }
        
        .table tr:hover {
            background: #f6f8fa;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            display: inline-block;
        }
        
        .status-active {
            background: #f6ffed;
            color: var(--success-color);
        }
        
        .status-inactive {
            background: #fff2f0;
            color: var(--error-color);
        }
        
        .status-draft {
            background: #f0f0f0;
            color: rgba(0, 0, 0, 0.65);
        }
        
        /* 表单组件 */
        .form-group {
            margin-bottom: var(--space-md);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--space-xs);
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            font-size: var(--font-size-base);
            transition: all 0.2s ease;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        /* 开关组件 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        .switch input:checked + .switch-slider {
            background-color: var(--primary-color);
        }
        
        .switch input:checked + .switch-slider:before {
            transform: translateX(22px);
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background-color: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-base);
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: var(--space-lg);
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: var(--font-size-xl);
            cursor: pointer;
            color: rgba(0, 0, 0, 0.45);
        }
        
        .modal-body {
            padding: var(--space-lg);
        }
        
        .modal-footer {
            padding: var(--space-lg);
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: var(--space-sm);
        }
        
        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }
        
        .stat-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-card);
            padding: var(--space-lg);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-xxl);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--space-xs);
        }
        
        .stat-label {
            color: rgba(0, 0, 0, 0.65);
            font-size: var(--font-size-sm);
        }
        
        /* 工具栏 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
            flex-wrap: wrap;
            gap: var(--space-sm);
        }
        
        .toolbar-left,
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }
        
        /* 搜索框 */
        .search-box {
            position: relative;
            width: 300px;
        }
        
        .search-input {
            width: 100%;
            padding: var(--space-sm) var(--space-md) var(--space-sm) 40px;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            font-size: var(--font-size-base);
        }
        
        .search-icon {
            position: absolute;
            left: var(--space-md);
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.45);
        }
        
        /* 筛选器 */
        .filter-group {
            display: flex;
            gap: var(--space-sm);
            align-items: center;
        }
        
        .filter-label {
            font-size: var(--font-size-sm);
            color: rgba(0, 0, 0, 0.65);
            white-space: nowrap;
        }
        
        /* 标签组 */
        .tag-group {
            display: flex;
            gap: var(--space-xs);
            flex-wrap: wrap;
        }
        
        .tag {
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            display: inline-block;
        }
        
        .tag-geo {
            background: #fff2f0;
            color: var(--violation-geo);
        }
        
        .tag-ownership {
            background: #fff7e6;
            color: var(--violation-ownership);
        }
        
        .tag-brand {
            background: #fff7e6;
            color: var(--violation-brand);
        }
        
        .tag-qualification {
            background: #fffbe6;
            color: var(--violation-qualification);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .nav-content {
                padding: 0 var(--space-md);
            }
            
            .page {
                padding: var(--space-md);
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                width: 100%;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .modal-content {
                width: 95%;
                margin: var(--space-md);
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        
        /* 加载状态 */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 审查结果页面专用样式 */
        .results-container {
            display: flex;
            gap: var(--space-md);
            height: calc(100vh - 300px);
            min-height: 600px;
        }

        /* 文档查看器 */
        .document-viewer {
            flex: 3;
            background: #f8f9fa;
            border-radius: var(--border-radius-lg);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: var(--box-shadow-card);
        }

        .document-header {
            background: white;
            padding: var(--space-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .document-title h3 {
            margin: 0 0 var(--space-sm) 0;
            color: var(--primary-color);
            font-size: var(--font-size-lg);
            font-weight: 600;
        }

        .document-meta {
            display: flex;
            gap: var(--space-lg);
            flex-wrap: wrap;
        }

        .meta-item {
            font-size: var(--font-size-sm);
            color: rgba(0, 0, 0, 0.65);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .document-controls {
            display: flex;
            gap: var(--space-xs);
        }

        .control-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            background: white;
            color: rgba(0, 0, 0, 0.65);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
        }

        .control-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: #f0f8ff;
        }

        .document-content {
            flex: 1;
            padding: var(--space-xl);
            overflow-y: auto;
            background: white;
            margin: var(--space-md);
            border-radius: var(--border-radius-base);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .document-section {
            margin-bottom: var(--space-xl);
        }

        .section-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-sm);
            border-bottom: 2px solid #f0f0f0;
        }

        .paragraph {
            position: relative;
            margin-bottom: var(--space-md);
            padding: var(--space-md);
            border-radius: var(--border-radius-base);
            transition: all 0.3s ease;
            cursor: pointer;
            line-height: 1.8;
        }

        .paragraph:hover {
            background: #f8f9fa;
        }

        .paragraph.has-issue {
            background: #fff8f0;
            border-left: 4px solid var(--warning-color);
        }

        .paragraph.highlighted {
            background: #e6f7ff;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        .line-number {
            display: inline-block;
            width: 50px;
            font-size: var(--font-size-sm);
            color: rgba(0, 0, 0, 0.45);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin-right: var(--space-sm);
        }

        .paragraph-content {
            display: inline;
        }

        .highlight-violation {
            background: linear-gradient(120deg, rgba(255, 77, 79, 0.15) 0%, rgba(255, 77, 79, 0.15) 100%);
            padding: 2px 4px;
            border-radius: 3px;
            border-bottom: 2px solid var(--error-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .highlight-violation:hover {
            background: linear-gradient(120deg, rgba(255, 77, 79, 0.25) 0%, rgba(255, 77, 79, 0.25) 100%);
        }

        .highlight-violation[data-type="geo"] {
            border-bottom-color: var(--violation-geo);
        }

        .highlight-violation[data-type="ownership"] {
            border-bottom-color: var(--violation-ownership);
        }

        .highlight-violation[data-type="brand"] {
            border-bottom-color: var(--violation-brand);
        }

        .highlight-violation[data-type="qualification"] {
            border-bottom-color: var(--violation-qualification);
        }

        .issue-indicator {
            position: absolute;
            top: var(--space-sm);
            right: var(--space-sm);
            background: var(--warning-color);
            color: white;
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        /* 问题分析面板 */
        .analysis-panel {
            flex: 2;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-card);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-header {
            padding: var(--space-lg);
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }

        .panel-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin: 0 0 var(--space-md) 0;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .issue-badge {
            background: var(--error-color);
            color: white;
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .panel-filters {
            display: flex;
            gap: var(--space-xs);
        }

        .filter-btn {
            padding: var(--space-xs) var(--space-md);
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            background: white;
            color: rgba(0, 0, 0, 0.65);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .filter-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .issue-list {
            flex: 1;
            padding: var(--space-sm);
            overflow-y: auto;
        }

        /* 问题卡片样式 */
        .issue-card {
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: var(--border-radius-base);
            margin-bottom: var(--space-sm);
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .issue-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .issue-card.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .issue-card[data-type="geo"] {
            border-left: 4px solid var(--violation-geo);
        }

        .issue-card[data-type="ownership"] {
            border-left: 4px solid var(--violation-ownership);
        }

        .issue-card[data-type="brand"] {
            border-left: 4px solid var(--violation-brand);
        }

        .issue-card[data-type="qualification"] {
            border-left: 4px solid var(--violation-qualification);
        }

        .issue-summary {
            padding: var(--space-md);
        }

        .issue-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-sm);
        }

        .issue-title-row {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            flex: 1;
        }

        .issue-icon {
            font-size: var(--font-size-lg);
        }

        .issue-title {
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            font-size: var(--font-size-base);
        }

        .issue-tags {
            display: flex;
            gap: var(--space-xs);
            align-items: center;
            flex-wrap: wrap;
        }

        .severity-tag {
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .severity-high {
            background: #fff2f0;
            color: var(--error-color);
        }

        .severity-medium {
            background: #fff7e6;
            color: var(--warning-color);
        }

        .severity-low {
            background: #f6ffed;
            color: var(--success-color);
        }

        .source-tag {
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .source-rule {
            background: #f6ffed;
            color: #389e0d;
        }

        .source-ai {
            background: #e6f7ff;
            color: var(--primary-color);
        }

        .confidence-tag {
            padding: 2px var(--space-sm);
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            border: 1px solid;
        }

        .confidence-high {
            border-color: var(--error-color);
            color: var(--error-color);
            background: #fff2f0;
        }

        .confidence-medium {
            border-color: var(--warning-color);
            color: var(--warning-color);
            background: #fff7e6;
        }

        .confidence-low {
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
            background: #fafafa;
        }

        .issue-location {
            font-size: var(--font-size-sm);
            color: rgba(0, 0, 0, 0.65);
            margin-bottom: var(--space-sm);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .issue-suggestion {
            background: #f6f8fa;
            padding: var(--space-sm);
            border-radius: var(--border-radius-base);
            margin-bottom: var(--space-sm);
        }

        .suggestion-label {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: var(--space-xs);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .suggestion-text {
            font-size: var(--font-size-sm);
            color: rgba(0, 0, 0, 0.65);
            line-height: 1.5;
        }

        .issue-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-sm) var(--space-md);
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
        }

        .action-buttons {
            display: flex;
            gap: var(--space-xs);
        }

        .action-btn {
            padding: var(--space-xs) var(--space-sm);
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius-base);
            background: white;
            color: rgba(0, 0, 0, 0.65);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .feedback-buttons {
            display: flex;
            gap: var(--space-xs);
        }

        .feedback-btn {
            padding: var(--space-xs);
            border: none;
            background: none;
            font-size: var(--font-size-base);
            cursor: pointer;
            border-radius: var(--border-radius-base);
            transition: all 0.2s ease;
        }

        .feedback-btn:hover {
            background: #e6f7ff;
        }

        /* 响应式设计 - 审查结果页面 */
        @media (max-width: 1024px) {
            .results-container {
                flex-direction: column;
                height: auto;
            }

            .document-viewer {
                flex: none;
                height: 400px;
            }

            .analysis-panel {
                flex: none;
                height: 500px;
            }
        }

        @media (max-width: 768px) {
            .document-header {
                flex-direction: column;
                gap: var(--space-md);
            }

            .document-meta {
                flex-direction: column;
                gap: var(--space-xs);
            }

            .issue-header {
                flex-direction: column;
                gap: var(--space-sm);
            }

            .issue-tags {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-nav">
        <div class="nav-content">
            <div class="nav-brand">
                📋 GovComply Web
            </div>
            <div class="nav-menu">
                <div class="nav-item active" data-page="upload">文档上传</div>
                <div class="nav-item" data-page="results">审查结果</div>
                <div class="nav-item" data-page="rules">规则管理</div>
                <div class="nav-item" data-page="files">文件管理</div>
            </div>
            <div class="nav-user">
                👤 张丽（采购专员）
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 文档上传页面 -->
        <div class="page active" id="upload-page">
            <div class="page-header">
                <h1 class="page-title">📄 文档上传</h1>
                <p class="page-description">上传政府采购文件进行合规性审查，支持.docx格式，最大10MB</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">上传文档</h3>
                </div>
                <div style="text-align: center; padding: 60px 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📁</div>
                    <p style="margin-bottom: 16px; color: rgba(0,0,0,0.65);">
                        拖拽文件到此处，或点击选择文件
                    </p>
                    <button class="btn btn-primary" onclick="selectFile()">
                        📎 选择文件
                    </button>
                    <input type="file" id="fileInput" style="display: none;" accept=".docx,.doc" onchange="handleFileSelect(event)">
                    <p style="margin-top: 16px; font-size: 12px; color: rgba(0,0,0,0.45);">
                        支持格式：.docx, .doc | 最大大小：10MB
                    </p>
                </div>
            </div>

            <!-- 最近上传 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">最近上传</h3>
                    <button class="btn" onclick="showPage('files')">查看全部</button>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>上传时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>政府采购招标文件.docx</td>
                                <td>2.3 MB</td>
                                <td>2025-01-11 14:30</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>
                                    <button class="btn btn-small" onclick="showPage('results')">查看结果</button>
                                </td>
                            </tr>
                            <tr>
                                <td>办公设备采购文件.docx</td>
                                <td>1.8 MB</td>
                                <td>2025-01-11 10:15</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>
                                    <button class="btn btn-small">查看结果</button>
                                </td>
                            </tr>
                            <tr>
                                <td>服务器采购招标.docx</td>
                                <td>3.1 MB</td>
                                <td>2025-01-10 16:45</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>
                                    <button class="btn btn-small">查看结果</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 审查结果页面 -->
        <div class="page" id="results-page">
            <div class="page-header">
                <h1 class="page-title">📋 审查结果</h1>
                <p class="page-description">政府采购招标文件.docx 的合规性审查结果</p>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">发现问题</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--error-color);">3</div>
                    <div class="stat-label">高风险</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--warning-color);">2</div>
                    <div class="stat-label">中风险</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--confidence-low);">1</div>
                    <div class="stat-label">低风险</div>
                </div>
            </div>

            <!-- 双栏布局 - 重新设计 -->
            <div class="results-container">
                <!-- 左栏：文档查看器 -->
                <div class="document-viewer">
                    <div class="document-header">
                        <div class="document-title">
                            <h3>📄 政府采购招标文件.docx</h3>
                            <div class="document-meta">
                                <span class="meta-item">📊 共检测 6 个段落</span>
                                <span class="meta-item">⚠️ 发现 6 个问题</span>
                                <span class="meta-item">⏱️ 处理时间 1分32秒</span>
                            </div>
                        </div>
                        <div class="document-controls">
                            <button class="control-btn" onclick="searchInDocument()">🔍</button>
                            <button class="control-btn" onclick="zoomOut()">🔍-</button>
                            <button class="control-btn" onclick="zoomIn()">🔍+</button>
                            <button class="control-btn" onclick="exportDocument()">📤</button>
                        </div>
                    </div>

                    <div class="document-content" id="documentContent">
                        <div class="document-section">
                            <h4 class="section-title">第一章 总则</h4>
                            <p class="paragraph" id="p1" data-paragraph="1">
                                <span class="line-number">P001</span>
                                <span class="paragraph-content">为规范政府采购行为，提升采购效率，保护供应商合法权益，根据《中华人民共和国政府采购法》及相关法规，制定本招标文件。</span>
                            </p>
                        </div>

                        <div class="document-section">
                            <h4 class="section-title">第二章 供应商资格要求</h4>
                            <p class="paragraph has-issue" id="p2" data-paragraph="2" data-issues="issue-1">
                                <span class="line-number">P016</span>
                                <span class="paragraph-content">
                                    供应商应满足以下基本条件：
                                    <span class="highlight-violation" data-issue="issue-1" data-type="geo">限定本地注册企业参与投标</span>，
                                    具有独立法人资格，财务状况良好。
                                </span>
                                <div class="issue-indicator">
                                    <span class="issue-count">1个问题</span>
                                </div>
                            </p>

                            <p class="paragraph has-issue" id="p3" data-paragraph="3" data-issues="issue-2">
                                <span class="line-number">P023</span>
                                <span class="paragraph-content">
                                    企业性质要求：
                                    <span class="highlight-violation" data-issue="issue-2" data-type="ownership">仅限国有企业参与本次采购</span>，
                                    民营企业和外资企业不得参与投标。
                                </span>
                                <div class="issue-indicator">
                                    <span class="issue-count">1个问题</span>
                                </div>
                            </p>
                        </div>

                        <div class="document-section">
                            <h4 class="section-title">第三章 采购需求</h4>
                            <p class="paragraph has-issue" id="p4" data-paragraph="4" data-issues="issue-3">
                                <span class="line-number">P035</span>
                                <span class="paragraph-content">
                                    本次采购的核心服务器产品，要求采用
                                    <span class="highlight-violation" data-issue="issue-3" data-type="brand">华为鲲鹏系列处理器</span>，
                                    以保证系统的稳定性和兼容性。操作系统必须为
                                    <span class="highlight-violation" data-issue="issue-4" data-type="brand">麒麟操作系统</span>。
                                </span>
                                <div class="issue-indicator">
                                    <span class="issue-count">2个问题</span>
                                </div>
                            </p>
                        </div>

                        <div class="document-section">
                            <h4 class="section-title">第四章 技术规格要求</h4>
                            <p class="paragraph has-issue" id="p5" data-paragraph="5" data-issues="issue-5">
                                <span class="line-number">P042</span>
                                <span class="paragraph-content">
                                    投标人必须提供过去三年内至少5个同类型项目业绩，且
                                    <span class="highlight-violation" data-issue="issue-5" data-type="qualification">单个合同金额不低于2000万元</span>。
                                    投标人必须是ISO9001认证企业。
                                </span>
                                <div class="issue-indicator">
                                    <span class="issue-count">1个问题</span>
                                </div>
                            </p>
                        </div>

                        <div class="document-section">
                            <h4 class="section-title">第五章 评分标准</h4>
                            <p class="paragraph has-issue" id="p6" data-paragraph="6" data-issues="issue-6">
                                <span class="line-number">P058</span>
                                <span class="paragraph-content">
                                    价格分(30分)，技术分(50分)，商务分(20分)。其中，在商务分中，
                                    <span class="highlight-violation" data-issue="issue-6" data-type="geo">凡在XX市本地注册的企业，可额外获得5分的加分</span>。
                                </span>
                                <div class="issue-indicator">
                                    <span class="issue-count">1个问题</span>
                                </div>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 右栏：问题分析面板 -->
                <div class="analysis-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">
                            审查结果
                            <span class="issue-badge">6</span>
                        </h3>
                        <div class="panel-filters">
                            <button class="filter-btn active" data-filter="all">全部</button>
                            <button class="filter-btn" data-filter="rule">规则</button>
                            <button class="filter-btn" data-filter="ai">AI</button>
                        </div>
                    </div>

                    <div class="issue-list" id="issueList">
                        <!-- 问题卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 操作栏 -->
            <div style="background: white; border-top: 1px solid #f0f0f0; padding: 16px 24px; margin-top: 16px; display: flex; justify-content: space-between; align-items: center; border-radius: 8px;">
                <div style="display: flex; align-items: center; gap: 24px;">
                    <span>📝 发现问题？</span>
                    <button class="btn">🏷️ 标记误报</button>
                    <button class="btn">➕ 标记漏报</button>
                </div>
                <div style="display: flex; align-items: center; gap: 24px; color: rgba(0,0,0,0.65); font-size: 12px;">
                    <span>⏱️ 处理时间: 1分32秒</span>
                    <span>💾 自动保存</span>
                    <span>🔄 最后更新: 刚刚</span>
                </div>
            </div>
        </div>

        <!-- 规则管理页面 -->
        <div class="page" id="rules-page">
            <div class="page-header">
                <h1 class="page-title">⚙️ 规则管理</h1> 
            </div>

            <!-- 规则库概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div class="stat-label">总规则数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--success-color);">18</div>
                    <div class="stat-label">已启用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--warning-color);">4</div>
                    <div class="stat-label">草稿</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--error-color);">2</div>
                    <div class="stat-label">已禁用</div>
                </div>
            </div>

            <!-- 规则管理工具栏 -->
            <div class="card">
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <span class="search-icon">🔍</span>
                            <input type="text" class="search-input" placeholder="搜索规则名称或描述..." id="ruleSearchInput">
                        </div>
                        <div class="filter-group">
                            <span class="filter-label">分类：</span>
                            <select class="form-select" style="width: 150px;" id="categoryFilter">
                                <option value="">全部分类</option>
                                <option value="geo">地域限制</option>
                                <option value="ownership">所有制歧视</option>
                                <option value="brand">品牌指向</option>
                                <option value="qualification">资质门槛</option>
                                <option value="performance">业绩要求</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <span class="filter-label">状态：</span>
                            <select class="form-select" style="width: 120px;" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">已启用</option>
                                <option value="inactive">已禁用</option>
                                <option value="draft">草稿</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right"> 
                        <button class="btn btn-primary" onclick="showRuleEditor()">➕ 新建规则</button>
                    </div>
                </div>

                <!-- 规则列表 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>规则名称</th>
                                <th>分类</th>
                                <th>优先级</th>
                                <th>状态</th>
                                <th>触发次数</th>
                                <th>准确率</th>
                                <th>最后修改</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <tr>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">限定注册地规则</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">检测招标文件中的地域限制条款</div>
                                    </div>
                                </td>
                                <td><span class="tag tag-geo">地域限制</span></td>
                                <td>高</td>
                                <td>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>156</td>
                                <td>94.2%</td>
                                <td>2025-01-10</td>
                                <td>
                                    <button class="btn btn-small" onclick="editRule('rule1')">✏️ 编辑</button>
                                    <button class="btn btn-small" onclick="testRule('rule1')">🧪 测试</button>
                                    <button class="btn btn-small" onclick="showRuleHistory('rule1')">📋 历史</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">所有制歧视检测</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">识别对企业所有制性质的限制</div>
                                    </div>
                                </td>
                                <td><span class="tag tag-ownership">所有制歧视</span></td>
                                <td>高</td>
                                <td>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>89</td>
                                <td>91.7%</td>
                                <td>2025-01-09</td>
                                <td>
                                    <button class="btn btn-small" onclick="editRule('rule2')">✏️ 编辑</button>
                                    <button class="btn btn-small" onclick="testRule('rule2')">🧪 测试</button>
                                    <button class="btn btn-small" onclick="showRuleHistory('rule2')">📋 历史</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">品牌指向识别</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">检测指定特定品牌的条款</div>
                                    </div>
                                </td>
                                <td><span class="tag tag-brand">品牌指向</span></td>
                                <td>中</td>
                                <td>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>67</td>
                                <td>88.1%</td>
                                <td>2025-01-08</td>
                                <td>
                                    <button class="btn btn-small" onclick="editRule('rule3')">✏️ 编辑</button>
                                    <button class="btn btn-small" onclick="testRule('rule3')">🧪 测试</button>
                                    <button class="btn btn-small" onclick="showRuleHistory('rule3')">📋 历史</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">注册资本门槛</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">检测过高的注册资本要求</div>
                                    </div>
                                </td>
                                <td><span class="tag tag-qualification">资质门槛</span></td>
                                <td>中</td>
                                <td>
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>23</td>
                                <td>76.3%</td>
                                <td>2025-01-07</td>
                                <td>
                                    <button class="btn btn-small" onclick="editRule('rule4')">✏️ 编辑</button>
                                    <button class="btn btn-small" onclick="testRule('rule4')">🧪 测试</button>
                                    <button class="btn btn-small" onclick="showRuleHistory('rule4')">📋 历史</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">成立年限要求</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">检测过高的企业成立年限要求</div>
                                    </div>
                                </td>
                                <td><span class="tag tag-qualification">资质门槛</span></td>
                                <td>低</td>
                                <td>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>45</td>
                                <td>82.4%</td>
                                <td>2025-01-06</td>
                                <td>
                                    <button class="btn btn-small" onclick="editRule('rule5')">✏️ 编辑</button>
                                    <button class="btn btn-small" onclick="testRule('rule5')">🧪 测试</button>
                                    <button class="btn btn-small" onclick="showRuleHistory('rule5')">📋 历史</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 文件管理页面 -->
        <div class="page" id="files-page">
            <div class="page-header">
                <h1 class="page-title">📁 文件管理</h1>
                <p class="page-description">管理已上传的政府采购文件，查看审查历史和结果</p>
            </div>

            <!-- 文件统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">127</div>
                    <div class="stat-label">总文件数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--success-color);">98</div>
                    <div class="stat-label">已审查</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--warning-color);">3</div>
                    <div class="stat-label">处理中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: var(--error-color);">26</div>
                    <div class="stat-label">待处理</div>
                </div>
            </div>

            <!-- 文件列表 -->
            <div class="card">
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <span class="search-icon">🔍</span>
                            <input type="text" class="search-input" placeholder="搜索文件名..." id="fileSearchInput">
                        </div>
                        <div class="filter-group">
                            <span class="filter-label">状态：</span>
                            <select class="form-select" style="width: 120px;" id="fileStatusFilter">
                                <option value="">全部状态</option>
                                <option value="completed">已完成</option>
                                <option value="processing">处理中</option>
                                <option value="pending">待处理</option>
                                <option value="failed">失败</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <span class="filter-label">时间：</span>
                            <select class="form-select" style="width: 120px;" id="timeFilter">
                                <option value="">全部时间</option>
                                <option value="today">今天</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn" onclick="batchDelete()">🗑️ 批量删除</button>
                        <button class="btn" onclick="batchExport()">📤 批量导出</button>
                        <button class="btn btn-primary" onclick="showPage('upload')">📎 上传文件</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>上传时间</th>
                                <th>状态</th>
                                <th>问题数</th>
                                <th>处理时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" class="file-checkbox"></td>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">政府采购招标文件.docx</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">办公设备采购项目</div>
                                    </div>
                                </td>
                                <td>2.3 MB</td>
                                <td>2025-01-11 14:30</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>6个问题</td>
                                <td>1分32秒</td>
                                <td>
                                    <button class="btn btn-small" onclick="showPage('results')">👁️ 查看</button>
                                    <button class="btn btn-small">📥 下载</button>
                                    <button class="btn btn-small btn-danger">🗑️ 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="file-checkbox"></td>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">办公设备采购文件.docx</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">计算机设备采购</div>
                                    </div>
                                </td>
                                <td>1.8 MB</td>
                                <td>2025-01-11 10:15</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>3个问题</td>
                                <td>58秒</td>
                                <td>
                                    <button class="btn btn-small">👁️ 查看</button>
                                    <button class="btn btn-small">📥 下载</button>
                                    <button class="btn btn-small btn-danger">🗑️ 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="file-checkbox"></td>
                                <td>
                                    <div>
                                        <div style="font-weight: 500;">服务器采购招标.docx</div>
                                        <div style="font-size: 12px; color: rgba(0,0,0,0.45);">数据中心设备采购</div>
                                    </div>
                                </td>
                                <td>3.1 MB</td>
                                <td>2025-01-10 16:45</td>
                                <td><span class="status-tag status-active">已完成</span></td>
                                <td>8个问题</td>
                                <td>2分15秒</td>
                                <td>
                                    <button class="btn btn-small">👁️ 查看</button>
                                    <button class="btn btn-small">📥 下载</button>
                                    <button class="btn btn-small btn-danger">🗑️ 删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则编辑器模态框 -->
    <div class="modal" id="ruleEditorModal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3 class="modal-title">⚙️ 规则编辑器</h3>
                <button class="modal-close" onclick="closeRuleEditor()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 24px;">
                    <!-- 左侧：规则配置 -->
                    <div style="flex: 1;">
                        <h4 style="margin-bottom: 16px;">基本信息</h4>
                        <div class="form-group">
                            <label class="form-label">规则名称</label>
                            <input type="text" class="form-input" placeholder="输入规则名称" value="限定注册地规则">
                        </div>
                        <div class="form-group">
                            <label class="form-label">规则描述</label>
                            <textarea class="form-textarea" placeholder="描述规则的用途和检测内容">检测招标文件中的地域限制条款，识别限定特定地区企业参与的违规内容</textarea>
                        </div>
                        <div style="display: flex; gap: 16px;">
                            <div class="form-group" style="flex: 1;">
                                <label class="form-label">分类</label>
                                <select class="form-select">
                                    <option value="geo" selected>地域限制</option>
                                    <option value="ownership">所有制歧视</option>
                                    <option value="brand">品牌指向</option>
                                    <option value="qualification">资质门槛</option>
                                </select>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <label class="form-label">优先级</label>
                                <select class="form-select">
                                    <option value="high" selected>高</option>
                                    <option value="medium">中</option>
                                    <option value="low">低</option>
                                </select>
                            </div>
                        </div>

                        <h4 style="margin: 24px 0 16px 0;">条件配置</h4>
                        <div class="form-group">
                            <label class="form-label">关键词匹配</label>
                            <textarea class="form-textarea" placeholder="输入关键词，用逗号分隔">本地注册,当地企业,本市企业,本省企业,注册地限制</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">正则表达式</label>
                            <input type="text" class="form-input" placeholder="输入正则表达式（可选）" value="(本地|当地|本市|本省).*(注册|企业|公司)">
                        </div>
                        <div class="form-group">
                            <label class="form-label">排除词</label>
                            <input type="text" class="form-input" placeholder="输入排除词，用逗号分隔" value="注册资本,注册商标">
                        </div>

                        <h4 style="margin: 24px 0 16px 0;">动作配置</h4>
                        <div class="form-group">
                            <label class="form-label">违规等级</label>
                            <select class="form-select">
                                <option value="high" selected>高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示信息</label>
                            <input type="text" class="form-input" value="发现地域限制条款，违反政府采购法相关规定">
                        </div>
                        <div class="form-group">
                            <label class="form-label">修改建议</label>
                            <textarea class="form-textarea">删除地域限制条款，改为描述功能性要求和技术标准</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">法规依据</label>
                            <input type="text" class="form-input" value="《政府采购法》第22条">
                        </div>
                    </div>

                    <!-- 右侧：测试区域 -->
                    <div style="flex: 0 0 400px; border-left: 1px solid #f0f0f0; padding-left: 24px;">
                        <h4 style="margin-bottom: 16px;">规则测试</h4>
                        <div class="form-group">
                            <label class="form-label">测试文本</label>
                            <textarea class="form-textarea" style="height: 150px;" placeholder="输入测试文本...">供应商应满足以下基本条件：限定本地注册企业参与投标，具有独立法人资格，财务状况良好。</textarea>
                        </div>
                        <button class="btn btn-primary" style="width: 100%; margin-bottom: 16px;" onclick="testRuleInEditor()">🧪 测试规则</button>

                        <div id="testResult" style="display: none;">
                            <h5 style="margin-bottom: 8px;">测试结果</h5>
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 12px; margin-bottom: 16px;">
                                <div style="color: var(--success-color); font-weight: 500; margin-bottom: 4px;">✅ 匹配成功</div>
                                <div style="font-size: 12px; color: rgba(0,0,0,0.65);">匹配到关键词："本地注册企业"</div>
                            </div>
                            <div style="background: #fff2e8; border-radius: 6px; padding: 12px;">
                                <div style="font-size: 12px; margin-bottom: 8px;">匹配内容：</div>
                                <div style="background: white; padding: 8px; border-radius: 4px; font-size: 12px;">
                                    供应商应满足以下基本条件：<span style="background: #ff4d4f20; padding: 2px 4px; border-radius: 3px;">限定本地注册企业参与投标</span>，具有独立法人资格，财务状况良好。
                                </div>
                            </div>
                        </div>

                        <h5 style="margin: 24px 0 8px 0;">示例文档</h5>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <div style="background: #f6f8fa; border-radius: 6px; padding: 12px; margin-bottom: 8px; cursor: pointer; font-size: 12px;" onclick="loadExample(1)">
                                <div style="font-weight: 500; margin-bottom: 4px;">示例1：地域限制</div>
                                <div style="color: rgba(0,0,0,0.65);">仅限本市注册企业参与投标...</div>
                            </div>
                            <div style="background: #f6f8fa; border-radius: 6px; padding: 12px; margin-bottom: 8px; cursor: pointer; font-size: 12px;" onclick="loadExample(2)">
                                <div style="font-weight: 500; margin-bottom: 4px;">示例2：注册地要求</div>
                                <div style="color: rgba(0,0,0,0.65);">供应商须在当地工商部门注册...</div>
                            </div>
                            <div style="background: #f6f8fa; border-radius: 6px; padding: 12px; margin-bottom: 8px; cursor: pointer; font-size: 12px;" onclick="loadExample(3)">
                                <div style="font-weight: 500; margin-bottom: 4px;">示例3：负面案例</div>
                                <div style="color: rgba(0,0,0,0.65);">注册资本不低于500万元...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeRuleEditor()">取消</button>
                <button class="btn" onclick="saveAsDraft()">保存为草稿</button>
                <button class="btn btn-primary" onclick="saveRule()">保存规则</button>
            </div>
        </div>
    </div>

    <!-- 规则导入模态框 -->
    <div class="modal" id="importModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">📥 导入规则</h3>
                <button class="modal-close" onclick="closeImportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">选择文件</label>
                    <input type="file" class="form-input" accept=".json,.yaml,.yml" id="importFileInput">
                    <div style="font-size: 12px; color: rgba(0,0,0,0.45); margin-top: 4px;">
                        支持格式：JSON, YAML
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">或粘贴规则内容</label>
                    <textarea class="form-textarea" style="height: 200px;" placeholder="粘贴YAML或JSON格式的规则内容..."></textarea>
                </div>
                <div style="background: #f6f8fa; border-radius: 6px; padding: 12px; margin-top: 16px;">
                    <div style="font-weight: 500; margin-bottom: 8px;">📋 导入说明</div>
                    <ul style="font-size: 12px; color: rgba(0,0,0,0.65); margin: 0; padding-left: 16px;">
                        <li>支持批量导入多个规则</li>
                        <li>重复规则将被跳过</li>
                        <li>导入前会进行格式验证</li>
                        <li>建议先备份现有规则</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeImportModal()">取消</button>
                <button class="btn btn-primary" onclick="importRules()">开始导入</button>
            </div>
        </div>
    </div>

    <!-- JavaScript交互逻辑 -->
    <script>
        // 页面导航
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageId + '-page').classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
        }

        // 导航点击事件
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                const pageId = this.dataset.page;
                showPage(pageId);
            });
        });

        // 文件选择
        function selectFile() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                // 模拟文件上传和处理
                alert(`文件 "${file.name}" 上传成功！正在进行合规性审查...`);
                // 3秒后跳转到结果页面
                setTimeout(() => {
                    showPage('results');
                }, 3000);
            }
        }

        // 规则管理功能
        function showRuleEditor(ruleId = null) {
            document.getElementById('ruleEditorModal').classList.add('show');
            if (ruleId) {
                // 加载现有规则数据
                console.log('编辑规则:', ruleId);
            }
        }

        function closeRuleEditor() {
            document.getElementById('ruleEditorModal').classList.remove('show');
        }

        function editRule(ruleId) {
            showRuleEditor(ruleId);
        }

        function testRule(ruleId) {
            alert(`测试规则: ${ruleId}`);
        }

        function showRuleHistory(ruleId) {
            alert(`查看规则历史: ${ruleId}`);
        }

        function testRuleInEditor() {
            document.getElementById('testResult').style.display = 'block';
        }

        function loadExample(exampleId) {
            const examples = {
                1: "仅限本市注册企业参与投标，外地企业不得参与本次采购活动。",
                2: "供应商须在当地工商部门注册，注册时间不少于2年。",
                3: "注册资本不低于500万元，具有良好的财务状况。"
            };

            const textarea = document.querySelector('#ruleEditorModal textarea');
            textarea.value = examples[exampleId];
        }

        function saveRule() {
            alert('规则保存成功！');
            closeRuleEditor();
        }

        function saveAsDraft() {
            alert('规则已保存为草稿！');
            closeRuleEditor();
        }

        // 规则导入导出
        function showImportModal() {
            document.getElementById('importModal').classList.add('show');
        }

        function closeImportModal() {
            document.getElementById('importModal').classList.remove('show');
        }

        function importRules() {
            alert('规则导入成功！');
            closeImportModal();
        }

        function exportRules() {
            alert('规则导出成功！');
        }

        // 文件管理功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.file-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function batchDelete() {
            const selected = document.querySelectorAll('.file-checkbox:checked');
            if (selected.length === 0) {
                alert('请选择要删除的文件');
                return;
            }
            if (confirm(`确定要删除选中的 ${selected.length} 个文件吗？`)) {
                alert('文件删除成功！');
            }
        }

        function batchExport() {
            const selected = document.querySelectorAll('.file-checkbox:checked');
            if (selected.length === 0) {
                alert('请选择要导出的文件');
                return;
            }
            alert(`正在导出 ${selected.length} 个文件...`);
        }

        // 搜索和筛选功能
        document.getElementById('ruleSearchInput')?.addEventListener('input', function() {
            // 实现规则搜索逻辑
            console.log('搜索规则:', this.value);
        });

        document.getElementById('fileSearchInput')?.addEventListener('input', function() {
            // 实现文件搜索逻辑
            console.log('搜索文件:', this.value);
        });

        // 筛选器变化事件
        document.getElementById('categoryFilter')?.addEventListener('change', function() {
            console.log('筛选分类:', this.value);
        });

        document.getElementById('statusFilter')?.addEventListener('change', function() {
            console.log('筛选状态:', this.value);
        });

        // 模态框点击外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('show');
                }
            });
        });

        // 审查结果页面专用功能
        const mockIssues = [
            {
                id: 'issue-1',
                paragraphId: 'p2',
                type: 'geo',
                title: '地域限制',
                severity: 'high',
                source: 'rule',
                icon: '📍',
                location: 'P016',
                excerpt: '限定本地注册企业参与投标',
                suggestion: '删除地域限制条款，改为描述功能性要求和技术标准',
                details: '根据《政府采购法》第22条，不得以注册地、分支机构所在地等对供应商实行地域歧视。'
            },
            {
                id: 'issue-2',
                paragraphId: 'p3',
                type: 'ownership',
                title: '所有制歧视',
                severity: 'high',
                source: 'ai',
                confidence: 'high',
                icon: '🏢',
                location: 'P023',
                excerpt: '仅限国有企业参与本次采购',
                suggestion: '修改为中性的资质要求，不区分企业所有制性质',
                details: 'AI分析：此条款明显给予特定所有制企业不公平的竞争优势，违反公平竞争原则。'
            },
            {
                id: 'issue-3',
                paragraphId: 'p4',
                type: 'brand',
                title: '品牌指向',
                severity: 'high',
                source: 'rule',
                icon: '🏷️',
                location: 'P035',
                excerpt: '华为鲲鹏系列处理器',
                suggestion: '修改为性能指标要求，避免指定特定品牌',
                details: '根据《政府采购法实施条例》第二十条，不得通过设定品牌限制对供应商实行差别待遇。'
            },
            {
                id: 'issue-4',
                paragraphId: 'p4',
                type: 'brand',
                title: '品牌指向',
                severity: 'medium',
                source: 'rule',
                icon: '🏷️',
                location: 'P035',
                excerpt: '麒麟操作系统',
                suggestion: '修改为功能性要求，如"支持国产化操作系统"',
                details: '直接指定操作系统品牌可能限制供应商选择，建议使用功能性描述。'
            },
            {
                id: 'issue-5',
                paragraphId: 'p5',
                type: 'qualification',
                title: '业绩门槛过高',
                severity: 'medium',
                source: 'ai',
                confidence: 'medium',
                icon: '📊',
                location: 'P042',
                excerpt: '单个合同金额不低于2000万元',
                suggestion: '建议降低业绩门槛或结合项目实际需求调整',
                details: 'AI分析：2000万的业绩门槛可能过高，有排斥中小企业的嫌疑。'
            },
            {
                id: 'issue-6',
                paragraphId: 'p6',
                type: 'geo',
                title: '地方保护',
                severity: 'high',
                source: 'rule',
                icon: '📍',
                location: 'P058',
                excerpt: '凡在XX市本地注册的企业，可额外获得5分的加分',
                suggestion: '删除地方保护性加分条款',
                details: '此条款属于典型的地方保护主义，严重违反公平竞争原则。'
            }
        ];

        // 渲染问题列表
        function renderIssueList() {
            const issueList = document.getElementById('issueList');
            if (!issueList) return;

            issueList.innerHTML = '';

            mockIssues.forEach(issue => {
                const card = createIssueCard(issue);
                issueList.appendChild(card);
            });
        }

        // 创建问题卡片
        function createIssueCard(issue) {
            const card = document.createElement('div');
            card.className = 'issue-card';
            card.id = issue.id;
            card.setAttribute('data-type', issue.type);
            card.setAttribute('data-paragraph', issue.paragraphId);

            // 构建置信度标签
            let confidenceTag = '';
            if (issue.source === 'ai' && issue.confidence) {
                confidenceTag = `<span class="confidence-tag confidence-${issue.confidence}">${issue.confidence === 'high' ? '高置信度' : issue.confidence === 'medium' ? '中置信度' : '低置信度'}</span>`;
            }

            card.innerHTML = `
                <div class="issue-summary">
                    <div class="issue-header">
                        <div class="issue-title-row">
                            <span class="issue-icon">${issue.icon}</span>
                            <span class="issue-title">${issue.title}</span>
                        </div>
                        <div class="issue-tags">
                            <span class="severity-tag severity-${issue.severity}">${issue.severity === 'high' ? '高风险' : issue.severity === 'medium' ? '中风险' : '低风险'}</span>
                            <span class="source-tag source-${issue.source}">${issue.source === 'rule' ? '规则' : 'AI'}</span>
                            ${confidenceTag}
                        </div>
                    </div>
                    <div class="issue-location">
                        📍 ${issue.location}: "${issue.excerpt}"
                    </div>
                    <div class="issue-suggestion">
                        <div class="suggestion-label">💡 修改建议</div>
                        <div class="suggestion-text">${issue.suggestion}</div>
                    </div>
                </div>
                <div class="issue-actions">
                    <div class="action-buttons">
                        <button class="action-btn" onclick="locateIssue('${issue.id}', '${issue.paragraphId}')">🎯 定位</button>
                        <button class="action-btn" onclick="showIssueDetails('${issue.id}')">📋 详情</button>
                    </div>
                    <div class="feedback-buttons">
                        <button class="feedback-btn" onclick="markIssue('${issue.id}', 'correct')" title="标记正确">👍</button>
                        <button class="feedback-btn" onclick="markIssue('${issue.id}', 'incorrect')" title="标记错误">👎</button>
                    </div>
                </div>
            `;

            // 添加点击事件
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.issue-actions')) {
                    locateIssue(issue.id, issue.paragraphId);
                }
            });

            return card;
        }

        // 定位问题到文档
        function locateIssue(issueId, paragraphId) {
            // 移除所有高亮
            document.querySelectorAll('.paragraph').forEach(p => {
                p.classList.remove('highlighted');
            });
            document.querySelectorAll('.issue-card').forEach(c => {
                c.classList.remove('active');
            });

            // 高亮目标段落
            const targetParagraph = document.getElementById(paragraphId);
            if (targetParagraph) {
                targetParagraph.classList.add('highlighted');
                targetParagraph.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }

            // 激活对应的问题卡片
            const targetCard = document.getElementById(issueId);
            if (targetCard) {
                targetCard.classList.add('active');
            }
        }

        // 显示问题详情
        function showIssueDetails(issueId) {
            const issue = mockIssues.find(i => i.id === issueId);
            if (issue) {
                alert(`问题详情：\n\n${issue.title}\n\n法规依据：\n${issue.details}`);
            }
        }

        // 标记问题反馈
        function markIssue(issueId, feedback) {
            const issue = mockIssues.find(i => i.id === issueId);
            if (issue) {
                const feedbackText = feedback === 'correct' ? '正确' : '错误';
                alert(`已标记问题"${issue.title}"为${feedbackText}，感谢您的反馈！`);
            }
        }

        // 文档控制功能
        function searchInDocument() {
            const searchTerm = prompt('请输入要搜索的内容：');
            if (searchTerm) {
                // 简单的搜索高亮实现
                const content = document.getElementById('documentContent');
                const text = content.innerHTML;
                const highlightedText = text.replace(
                    new RegExp(searchTerm, 'gi'),
                    `<mark style="background: yellow; padding: 2px 4px; border-radius: 3px;">$&</mark>`
                );
                content.innerHTML = highlightedText;
            }
        }

        function zoomIn() {
            const content = document.getElementById('documentContent');
            const currentSize = parseFloat(window.getComputedStyle(content).fontSize);
            content.style.fontSize = (currentSize + 2) + 'px';
        }

        function zoomOut() {
            const content = document.getElementById('documentContent');
            const currentSize = parseFloat(window.getComputedStyle(content).fontSize);
            content.style.fontSize = Math.max(12, currentSize - 2) + 'px';
        }

        function exportDocument() {
            alert('正在导出带批注的Word文档...\n\n（原型功能）批注版.docx 已开始下载。');
        }

        // 筛选功能
        function setupFilters() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮状态
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 筛选问题卡片
                    const filter = this.getAttribute('data-filter');
                    const cards = document.querySelectorAll('.issue-card');

                    cards.forEach(card => {
                        if (filter === 'all') {
                            card.style.display = 'block';
                        } else {
                            const source = card.querySelector('.source-tag').textContent.trim();
                            const shouldShow = (filter === 'rule' && source === '规则') ||
                                             (filter === 'ai' && source === 'AI');
                            card.style.display = shouldShow ? 'block' : 'none';
                        }
                    });
                });
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('GovComply Web v3.0 原型已加载');

            // 如果在审查结果页面，初始化相关功能
            if (document.getElementById('issueList')) {
                renderIssueList();
                setupFilters();
            }
        });
    </script>
</body>
</html>
