<template>
  <BaseDrawer
    v-model="visible"
    title="审查清单"
    :width="640"
    :loading="loading"
    :ok-visible="true"
    :cancel-visible="false"
    :ok-loading="saving"
    :ok-disabled="isSkeleton || !hasChanges"
    ok-text="重新审查"
    @ok="handleSave"
    @cancel="handleClose"
  >
    <div class="checklist-content"> 
      <div class="form-section">
        <div class="form-label">审查方式：</div>
        <a-select
          v-model:value="reviewMethod"
          placeholder="请选择审查方式"
          style="width: 320px"
          :disabled="true"
          :options="reviewMethodOptions"
        />
      </div>
      <div class="form-section">
        <div class="form-label">审查清单：</div>
        <a-select
          v-model:value="reviewContent"
          placeholder="请选择审查清单"
          style="width: 320px"
          :disabled="true"
          :options="reviewContentOptions"
        />
      </div>
      <div class="form-section">
        <div class="form-label">审查项：</div>
      </div>

      <!-- 审查项目列表 -->
      <div class="checklist-items">
        <a-checkbox-group
          v-model:value="selectedCheckPoints"
          :disabled="isSkeleton"
          class="checkbox-group"
        >
          <!-- 按分类显示审查项目 -->
          <div
            v-for="category in groupedCheckPoints"
            :key="category.reviewItemCode"
            class="category-section"
          >
            <h4 class="category-title">{{ category.reviewItemName }}</h4>
            <div class="category-items">
              <div
                v-for="checkPoint in category.children"
                :key="checkPoint.sceneId"
                class="checklist-item"
              >
                <a-checkbox
                  :value="checkPoint.sceneId"
                  class="checkbox-item"
                >
                  <div class="point-name">{{ checkPoint.sceneDesc }}</div>
                </a-checkbox>
              </div>
            </div>
          </div>
        </a-checkbox-group>

        <!-- 空状态 -->
        <div v-if="checkPointList.length === 0 && !loading" class="empty-state">
          <a-empty description="暂无审查项目" />
        </div>
        <!-- 确认弹框 -->
        <ReviewSuccessModal v-model:open="showReviewSuccessModal"/>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch , nextTick} from 'vue' 
import { useRoute, useRouter } from 'vue-router'
import BaseDrawer from '@/components/BaseDrawer/base-drawer.vue'
import { checkSceneDetail,reviewAgain } from '@/api/examine' 
import ReviewSuccessModal from './ReviewSuccessModal.vue' 

defineOptions({
  name: 'CheckListModal'
})

// 审查点数据类型
interface CheckPoint {
  reviewItemCode: string
  sceneId?: string
  sceneDesc?: string 
  status?: number
  reviewItemName: string,
  children?: CheckPoint[]
}

interface Props {
  open: boolean
  taskId: string
  isSkeleton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isSkeleton: false
})
const emit = defineEmits<{
  'update:open': [value: boolean]
  'save': [data: any]
}>()
const router = useRouter()
// 响应式状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const saving = ref(false)

// 审查方式和清单选项
const reviewMethod = ref('')
const reviewContent = ref('')

const reviewMethodOptions = []

const reviewContentOptions = []

// 审查点数据
const checkPointList = ref<CheckPoint[]>([])
const selectedCheckPoints = ref<any[]>([]) // 选中的审查点ID列表

// 按reviewItemCode分组的审查项目
const groupedCheckPoints = computed(() => {
  let groups: CheckPoint[] = checkPointList.value || [] 
  groups = groups.reduce((acc, item) => {
    let group = acc.find(group => group.reviewItemCode === item.reviewItemCode);
    if (!group) {
      group = {
        reviewItemCode: item.reviewItemCode,
        reviewItemName: item.reviewItemName,
        children: []
      };
      acc.push(group);
    }
    group.children?.push(item);
    return acc;
  }, [] as CheckPoint[]);   
  return groups
})

// 方法

const handleClose = () => {
  visible.value = false
}

const showReviewSuccessModal = ref(false)
const handleSave = async () => { 
  saving.value = true 
  showReviewSuccessModal.value = false
  const {data, err} = await reviewAgain({taskId: props.taskId,sceneIdList: selectedCheckPoints.value})
  saving.value = false
  if(err) return
  showReviewSuccessModal.value = true 
}
const cachedCheckPoints = ref<any[]>([])
const hasChanges = computed(() => {
  if (cachedCheckPoints.value.length !== selectedCheckPoints.value.length) {
    return true
  }
  return !cachedCheckPoints.value.every(id => selectedCheckPoints.value.includes(id))
})
// 加载审查点数据
const loadCheckPoints = async () => {
    loading.value = true   
    const {data, err} = await checkSceneDetail({taskId: props.taskId})
    loading.value = false
    checkPointList.value = data?.sceneList || [] 
    reviewMethod.value = data.procurementMethod || ''
    reviewContent.value = data.checkListName || ''  
    // 默认全选
    const selected = checkPointList.value.filter(item=>item.status===1).map(item => item.sceneId) ?? []
    selectedCheckPoints.value = [...selected]
    cachedCheckPoints.value = [...selected]
    
}

// 监听抽屉打开状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    loadCheckPoints()
  }
})
watch(() => showReviewSuccessModal.value, (newVal, oldVal) => {
  if (oldVal && !newVal) {
    // 弹窗从打开变为关闭
    nextTick(() => {
      router.push({name: 'LibraryIndex'})
    })
  }
})
</script>

<style lang="scss" scoped>
.checklist-content {
  padding: 24px;

  .form-section {
    display: flex;
    align-items: center;
    margin-bottom: 16px; 
  }

  .checklist-items {
    .checkbox-group {
      display: flex;
      flex-direction: column; 
    }

    .category-section {
      .category-title { 
        font-weight: 600;  
        padding: 8px;
        color: #000000E0;
        background-color: #00000005;
        border-bottom: 1px solid #F0F0F0;
      } 
    }

    .checklist-item {
      padding:12px 0;  
      border-bottom:1px solid #F0F0F0;
      .point-name {
        white-space: break-spaces;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: var(--text-3, #999);
    }
  } 
} 
</style>
